#!/usr/bin/env python3
"""
Roshambo Flask API for Reinvent Scoring.
Clean production-ready implementation.
"""

import os
import shutil
from pathlib import Path
from flask import Flask, request, jsonify
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
ROSHAMBO_WORKING_DIR = os.path.join(os.path.dirname(__file__), "inpdata")
Path(ROSHAMBO_WORKING_DIR).mkdir(parents=True, exist_ok=True)

def check_gpu_availability():
    """Check GPU availability."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            return 0, gpu_count  # Default to GPU 0
        else:
            return None, 0
    except ImportError:
        return None, 0

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    try:
        preferred_gpu, gpu_count = check_gpu_availability()
        
        # Check roshambo availability
        roshambo_available = False
        try:
            from roshambo.api import get_similarity_scores
            roshambo_available = True
        except ImportError:
            pass

        return jsonify({
            "status": "healthy",
            "service": "roshambo-api",
            "gpu_count": gpu_count,
            "preferred_gpu": preferred_gpu,
            "roshambo_available": roshambo_available,
            "working_dir": ROSHAMBO_WORKING_DIR
        })
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return jsonify({
            "status": "error",
            "service": "roshambo-api",
            "error": str(e)
        }), 500

@app.route('/similarity', methods=['POST'])
def calculate_similarity():
    """Calculate molecular shape similarity using Roshambo."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No JSON data provided"}), 400

        # Extract parameters
        reference_file = data.get("reference_file")
        dataset_file = data.get("dataset_file")
        working_dir = data.get("working_dir", ROSHAMBO_WORKING_DIR)

        # Validate required parameters
        if not reference_file:
            return jsonify({"success": False, "error": "reference_file is required"}), 400
        if not dataset_file:
            return jsonify({"success": False, "error": "dataset_file is required"}), 400

        # Ensure working directory exists
        Path(working_dir).mkdir(parents=True, exist_ok=True)

        # Validate files exist
        if not os.path.exists(reference_file):
            return jsonify({"success": False, "error": f"Reference file not found: {reference_file}"}), 400
        if not os.path.exists(dataset_file):
            return jsonify({"success": False, "error": f"Dataset file not found: {dataset_file}"}), 400

        # Determine GPU ID
        gpu_id = data.get("gpu_id", 0)

        # Prepare roshambo parameters
        roshambo_params = {
            "ref_file": reference_file,
            "dataset_files_pattern": dataset_file,
            "ignore_hs": data.get("ignore_hs", True),
            "n_confs": data.get("n_confs", 0),
            "use_carbon_radii": data.get("use_carbon_radii", True),
            "color": data.get("color", True),
            "sort_by": data.get("sort_by", "ComboTanimoto"),
            "write_to_file": data.get("write_to_file", True),
            "gpu_id": gpu_id,
            "working_dir": working_dir
        }

        logger.info(f"Processing similarity calculation with GPU {gpu_id}")

        # Call roshambo
        result = call_roshambo_api(**roshambo_params)

        if result["success"]:
            return jsonify({
                "success": True,
                "message": "Similarity calculation completed",
                "working_dir": working_dir,
                "output_files": {
                    "csv": os.path.join(working_dir, "roshambo.csv"),
                    "mols_sdf": os.path.join(working_dir, "mols.sdf"),
                    "hits_sdf": os.path.join(working_dir, "hits.sdf")
                },
                "gpu_used": gpu_id
            })
        else:
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"Error in similarity calculation: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def call_roshambo_api(**kwargs):
    """Call the roshambo API with the given parameters."""
    original_cwd = os.getcwd()
    working_dir = kwargs.get("working_dir", ROSHAMBO_WORKING_DIR)
    
    try:
        # Change to working directory
        os.chdir(working_dir)
        logger.info(f"Changed to working directory: {working_dir}")
        
        # Import roshambo
        from roshambo.api import get_similarity_scores
        
        # Call roshambo (returns None but writes files)
        get_similarity_scores(**kwargs)
        
        # Check if output files were created
        expected_files = ["roshambo.csv", "mols.sdf", "hits.sdf"]
        created_files = []
        
        for filename in expected_files:
            if os.path.exists(filename):
                created_files.append(filename)
                logger.info(f"Created file: {filename}")
        
        if created_files:
            return {
                "success": True,
                "message": f"Roshambo completed successfully. Created files: {created_files}",
                "created_files": created_files
            }
        else:
            return {
                "success": False,
                "error": "Roshambo completed but no output files were created"
            }
            
    except ImportError as e:
        logger.error(f"Roshambo import error: {e}")
        return {
            "success": False,
            "error": f"Roshambo not available: {e}"
        }
    except Exception as e:
        logger.error(f"Roshambo execution error: {e}")
        return {
            "success": False,
            "error": f"Roshambo execution failed: {e}"
        }
    finally:
        os.chdir(original_cwd)

if __name__ == '__main__':
    print("Starting Roshambo Flask API on 127.0.0.1:5000")
    print("Endpoints:")
    print("  GET  /health     - Health check")
    print("  POST /similarity - Calculate similarity")
    
    # Run the Flask app
    app.run(host='127.0.0.1', port=5000, debug=True)
