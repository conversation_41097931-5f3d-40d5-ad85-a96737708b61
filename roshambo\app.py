#!/usr/bin/env python3
"""
Roshambo Flask API for Reinvent Scoring - Rebuilt and Optimized.
This API provides a RESTful interface to the Roshambo shape similarity engine.
Explicitly uses GPU 1 or 2 by default (not GPU 0) for better performance.
"""

import os
import sys
import shutil
import time
import json
import subprocess
from pathlib import Path
from flask import Flask, request, jsonify
import logging

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('roshambo_api.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
ROSHAMBO_WORKING_DIR = os.path.join(os.path.dirname(__file__), "inpdata")
Path(ROSHAMBO_WORKING_DIR).mkdir(parents=True, exist_ok=True)

def check_gpu_availability():
    """Check GPU availability and return preferred GPU ID (1 or 2, not 0)."""
    try:
        logger.info("🔍 Checking GPU availability...")
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"✅ GPU available: {gpu_count} device(s)")

            # Prefer GPU 1 or 2, avoid GPU 0
            if gpu_count >= 3:
                preferred_gpu = 2
                logger.info("🎯 Using GPU 2 (preferred for multi-GPU systems)")
            elif gpu_count >= 2:
                preferred_gpu = 1
                logger.info("🎯 Using GPU 1 (preferred over GPU 0)")
            else:
                logger.warning("⚠️ Only GPU 0 available, using it as fallback")
                preferred_gpu = 0

            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(f"  GPU {i}: {gpu_name} ({memory_total:.1f} GB)")

            return preferred_gpu, gpu_count
        else:
            logger.warning("⚠️ No GPU available, will use CPU")
            return None, 0
    except ImportError:
        logger.warning("⚠️ PyTorch not available, cannot check GPU")
        return None, 0
    except Exception as e:
        logger.error(f"❌ Error checking GPU: {e}")
        return None, 0

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint with comprehensive GPU and system status."""
    try:
        logger.info("🏥 Health check requested")
        preferred_gpu, gpu_count = check_gpu_availability()

        # Check roshambo availability
        roshambo_available = False
        roshambo_error = None
        try:
            # Test if roshambo can be imported
            from roshambo.api import get_similarity_scores
            roshambo_available = True
            logger.info("✅ Roshambo API import successful")
        except ImportError as e:
            roshambo_error = f"Import error: {e}"
            logger.error(f"❌ Roshambo import failed: {e}")
        except Exception as e:
            roshambo_error = f"General error: {e}"
            logger.error(f"❌ Roshambo check failed: {e}")

        # Check working directory
        working_dir_status = "accessible" if os.path.exists(ROSHAMBO_WORKING_DIR) else "not accessible"

        # System info
        try:
            import psutil
            memory_info = psutil.virtual_memory()
            disk_info = psutil.disk_usage('/')
            memory_percent = memory_info.percent
            disk_percent = disk_info.percent
        except ImportError:
            memory_percent = "unknown"
            disk_percent = "unknown"

        health_data = {
            "status": "healthy" if roshambo_available else "degraded",
            "service": "roshambo-api",
            "timestamp": time.time(),
            "gpu": {
                "count": gpu_count,
                "preferred_gpu": preferred_gpu,
                "available": gpu_count > 0
            },
            "roshambo": {
                "available": roshambo_available,
                "error": roshambo_error
            },
            "system": {
                "working_dir": ROSHAMBO_WORKING_DIR,
                "working_dir_status": working_dir_status,
                "memory_usage_percent": memory_percent,
                "disk_usage_percent": disk_percent
            }
        }

        logger.info(f"🏥 Health check completed: {health_data['status']}")
        return jsonify(health_data)

    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return jsonify({
            "status": "error",
            "service": "roshambo-api",
            "error": str(e),
            "timestamp": time.time()
        }), 500

@app.route('/similarity', methods=['POST'])
def calculate_similarity():
    """
    Calculate molecular shape similarity using Roshambo with comprehensive error handling.

    Expected JSON payload:
    {
        "reference_file": "path/to/reference.sdf",
        "dataset_file": "path/to/dataset.smi or dataset.sdf",
        "ignore_hs": true,
        "n_confs": 0,
        "use_carbon_radii": true,
        "color": true,
        "sort_by": "ComboTanimoto",
        "write_to_file": true,
        "gpu_id": 1,  # Default to GPU 1 or 2, not 0
        "working_dir": "path/to/working/dir"
    }
    """
    request_start_time = time.time()
    logger.info("🧮 Similarity calculation request received")

    try:
        # Validate request content type
        if not request.is_json:
            logger.error("❌ Request is not JSON")
            return jsonify({"success": False, "error": "Request must be JSON"}), 400

        data = request.get_json()
        if not data:
            logger.error("❌ No JSON data provided")
            return jsonify({"success": False, "error": "No JSON data provided"}), 400

        logger.info(f"📋 Request data: {json.dumps(data, indent=2)}")

        # Extract and validate required parameters
        try:
            reference_file = data.get("reference_file")
            dataset_file = data.get("dataset_file")
            working_dir = data.get("working_dir", ROSHAMBO_WORKING_DIR)

            logger.info(f"📁 Working directory: {working_dir}")
            logger.info(f"📄 Reference file: {reference_file}")
            logger.info(f"📄 Dataset file: {dataset_file}")

        except Exception as e:
            logger.error(f"❌ Error extracting parameters: {e}")
            return jsonify({"success": False, "error": f"Error extracting parameters: {e}"}), 400

        # Validate required parameters
        if not reference_file:
            logger.error("❌ reference_file is required")
            return jsonify({"success": False, "error": "reference_file is required"}), 400
        if not dataset_file:
            logger.error("❌ dataset_file is required")
            return jsonify({"success": False, "error": "dataset_file is required"}), 400

        # Ensure working directory exists
        try:
            Path(working_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ Working directory ready: {working_dir}")
        except Exception as e:
            logger.error(f"❌ Failed to create working directory: {e}")
            return jsonify({"success": False, "error": f"Failed to create working directory: {e}"}), 500

        # Handle file paths - make them absolute if relative to working directory
        try:
            if not os.path.isabs(reference_file):
                reference_file = os.path.join(working_dir, reference_file)
            if not os.path.isabs(dataset_file):
                dataset_file = os.path.join(working_dir, dataset_file)
        except Exception as e:
            logger.error(f"❌ Error processing file paths: {e}")
            return jsonify({"success": False, "error": f"Error processing file paths: {e}"}), 400

        # Validate files exist and are readable
        try:
            if not os.path.exists(reference_file):
                logger.error(f"❌ Reference file not found: {reference_file}")
                return jsonify({"success": False, "error": f"Reference file not found: {reference_file}"}), 400
            if not os.path.exists(dataset_file):
                logger.error(f"❌ Dataset file not found: {dataset_file}")
                return jsonify({"success": False, "error": f"Dataset file not found: {dataset_file}"}), 400
        except Exception as e:
            logger.error(f"❌ Error validating files: {e}")
            return jsonify({"success": False, "error": f"Error validating files: {e}"}), 400

        logger.info(f"✅ Reference file: {reference_file}")
        logger.info(f"✅ Dataset file: {dataset_file}")

        # Determine GPU ID - prefer GPU 1 or 2, not 0
        try:
            requested_gpu = data.get("gpu_id")
            if requested_gpu is None:
                # Auto-select preferred GPU
                preferred_gpu, _ = check_gpu_availability()
                gpu_id = preferred_gpu if preferred_gpu is not None else 0
            else:
                gpu_id = max(0, int(requested_gpu))

            logger.info(f"🎯 Using GPU ID: {gpu_id}")
        except (ValueError, TypeError) as e:
            logger.error(f"❌ Invalid GPU ID: {e}")
            return jsonify({"success": False, "error": f"Invalid GPU ID: {e}"}), 400

        # Prepare roshambo parameters
        try:
            roshambo_params = {
                "ref_file": reference_file,
                "dataset_files_pattern": dataset_file,
                "ignore_hs": data.get("ignore_hs", True),
                "n_confs": max(0, int(data.get("n_confs", 0))),
                "use_carbon_radii": data.get("use_carbon_radii", True),
                "color": data.get("color", True),
                "sort_by": data.get("sort_by", "ComboTanimoto"),
                "write_to_file": data.get("write_to_file", True),
                "gpu_id": gpu_id,
                "working_dir": working_dir
            }
            logger.info(f"🔧 Roshambo parameters: {roshambo_params}")
        except (ValueError, TypeError) as e:
            logger.error(f"❌ Invalid parameter values: {e}")
            return jsonify({"success": False, "error": f"Invalid parameter values: {e}"}), 400

        # Call roshambo API
        try:
            logger.info("🚀 Starting Roshambo API call...")
            result = call_roshambo_api(**roshambo_params)
        except Exception as e:
            logger.error(f"❌ Error calling roshambo API: {e}")
            return jsonify({"success": False, "error": f"Roshambo API call failed: {e}"}), 500

        # Process result
        if result.get("success"):
            execution_time = time.time() - request_start_time
            logger.info(f"✅ Similarity calculation completed in {execution_time:.2f} seconds")
            return jsonify({
                "success": True,
                "message": "Similarity calculation completed",
                "working_dir": working_dir,
                "output_files": {
                    "csv": os.path.join(working_dir, "roshambo.csv"),
                    "mols_sdf": os.path.join(working_dir, "mols.sdf"),
                    "hits_sdf": os.path.join(working_dir, "hits.sdf")
                },
                "gpu_used": gpu_id,
                "file_status": result.get("file_status", {}),
                "execution_time": execution_time
            })
        else:
            logger.error(f"❌ Roshambo API failed: {result}")
            return jsonify(result), 500

    except Exception as e:
        execution_time = time.time() - request_start_time
        logger.error(f"❌ Critical error in similarity calculation: {e}")
        return jsonify({
            "success": False,
            "error": f"Critical error: {str(e)}",
            "execution_time": execution_time
        }), 500

def call_roshambo_api(**kwargs):
    """
    Call the roshambo API with comprehensive error handling and process management.

    How roshambo works:
    - Working directory path contains dataset.smi and reference.sdf file
    - Roshambo creates CSV, mols.sdf, and hits.sdf files in the working directory
    """
    original_cwd = None
    start_time = time.time()

    try:
        logger.info("🔧 Setting up Roshambo API call...")

        # Validate working directory
        working_dir = kwargs.get("working_dir", ROSHAMBO_WORKING_DIR)
        if not working_dir:
            logger.error("❌ No working directory specified")
            return {
                "success": False,
                "error": "No working directory specified"
            }

        # Ensure working directory exists
        try:
            Path(working_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ Working directory ready: {working_dir}")
        except Exception as e:
            logger.error(f"❌ Failed to create working directory {working_dir}: {e}")
            return {
                "success": False,
                "error": f"Failed to create working directory: {e}"
            }

        # Change to working directory with try-catch
        original_cwd = os.getcwd()
        try:
            os.chdir(working_dir)
            logger.info(f"📁 Changed to working directory: {working_dir}")
        except Exception as e:
            logger.error(f"❌ Failed to change to working directory {working_dir}: {e}")
            return {
                "success": False,
                "error": f"Failed to change to working directory: {e}"
            }

        # Validate required parameters
        required_params = ["ref_file", "dataset_files_pattern"]
        missing_params = [param for param in required_params if not kwargs.get(param)]
        if missing_params:
            logger.error(f"❌ Missing required parameters: {missing_params}")
            return {
                "success": False,
                "error": f"Missing required parameters: {missing_params}"
            }

        # Validate input files exist in working directory
        try:
            ref_file = kwargs.get("ref_file")
            dataset_file = kwargs.get("dataset_files_pattern")

            # Check if files exist (they should be absolute paths or in working dir)
            if not os.path.isabs(ref_file):
                ref_file_path = os.path.join(working_dir, ref_file)
            else:
                ref_file_path = ref_file

            if not os.path.isabs(dataset_file):
                dataset_file_path = os.path.join(working_dir, dataset_file)
            else:
                dataset_file_path = dataset_file

            if not os.path.exists(ref_file_path):
                logger.error(f"❌ Reference file not found: {ref_file_path}")
                return {
                    "success": False,
                    "error": f"Reference file not found: {ref_file_path}"
                }

            if not os.path.exists(dataset_file_path):
                logger.error(f"❌ Dataset file not found: {dataset_file_path}")
                return {
                    "success": False,
                    "error": f"Dataset file not found: {dataset_file_path}"
                }

            logger.info(f"✅ Input files validated - Ref: {ref_file_path}, Dataset: {dataset_file_path}")

        except Exception as e:
            logger.error(f"❌ Error validating input files: {e}")
            return {
                "success": False,
                "error": f"Error validating input files: {e}"
            }

        logger.info(f"🚀 Calling roshambo with parameters: {kwargs}")

        # Call roshambo using subprocess to avoid threading/signal issues
        try:
            # Set adaptive timeout based on dataset size
            base_timeout = 600  # 10 minutes base
            n_confs = kwargs.get("n_confs", 0)
            timeout_seconds = base_timeout + (n_confs * 30)  # Add 30s per conformer

            logger.info(f"⏱️ Starting roshambo execution (max {timeout_seconds}s)...")

            try:
                # Import roshambo here to handle import errors gracefully
                from roshambo.api import get_similarity_scores

                logger.info("📦 Roshambo API imported successfully")

                # Call roshambo directly (returns None but writes files)
                logger.info("🔄 Executing Roshambo similarity calculation...")
                get_similarity_scores(**kwargs)

                execution_time = time.time() - start_time
                logger.info(f"✅ Roshambo execution completed in {execution_time:.2f} seconds")

            except ImportError as e:
                logger.error(f"❌ Roshambo import failed: {e}")
                return {
                    "success": False,
                    "error": f"Roshambo not available: {e}"
                }
            except Exception as e:
                logger.error(f"❌ Roshambo execution failed: {e}")
                return {
                    "success": False,
                    "error": f"Roshambo execution failed: {e}"
                }

        except Exception as e:
            logger.error(f"❌ Error setting up roshambo execution: {e}")
            return {
                "success": False,
                "error": f"Error setting up roshambo execution: {e}"
            }

        # Check if output files were created with detailed validation
        expected_files = ["roshambo.csv", "mols.sdf", "hits.sdf"]
        created_files = []
        file_status = {}

        for filename in expected_files:
            try:
                if os.path.exists(filename):  # Check in current working directory
                    file_size = os.path.getsize(filename)
                    if file_size > 0:
                        created_files.append(filename)
                        file_status[filename] = f"created ({file_size} bytes)"
                        logger.info(f"✅ Created file: {filename} ({file_size} bytes)")
                    else:
                        file_status[filename] = "empty file"
                        logger.warning(f"⚠️ Created empty file: {filename}")
                else:
                    file_status[filename] = "not created"
                    logger.warning(f"⚠️ File not created: {filename}")
            except Exception as e:
                file_status[filename] = f"error checking: {e}"
                logger.error(f"❌ Error checking file {filename}: {e}")

        execution_time = time.time() - start_time

        if created_files:
            logger.info(f"✅ Roshambo completed successfully. Created files: {created_files}")
            return {
                "success": True,
                "message": f"Roshambo completed successfully. Created files: {created_files}",
                "created_files": created_files,
                "file_status": file_status,
                "execution_time": execution_time
            }
        else:
            logger.error("❌ Roshambo completed but no valid output files were created")
            return {
                "success": False,
                "error": "Roshambo completed but no valid output files were created",
                "file_status": file_status,
                "execution_time": execution_time
            }

    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"❌ Critical error in call_roshambo_api: {e}")
        return {
            "success": False,
            "error": f"Critical error: {e}",
            "execution_time": execution_time
        }
    finally:
        # Always restore original working directory with try-catch
        if original_cwd and os.path.exists(original_cwd):
            try:
                os.chdir(original_cwd)
                logger.info(f"📁 Restored working directory to: {original_cwd}")
            except Exception as e:
                logger.error(f"❌ Failed to restore original working directory: {e}")

def move_roshambo_outputs(target_dir):
    """
    Move roshambo output files to the target directory.
    Roshambo creates mols.sdf, hits.sdf, and roshambo.csv in the working directory.
    """
    try:
        # Validate target directory
        if not target_dir:
            logger.error("❌ No target directory specified for moving output files")
            return

        # Ensure target directory exists
        try:
            Path(target_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            logger.error(f"❌ Failed to create target directory {target_dir}: {e}")
            return

        # Roshambo output files
        output_files = ["mols.sdf", "hits.sdf", "roshambo.csv"]
        moved_files = []
        failed_files = []

        for filename in output_files:
            try:
                # Check if file exists in current directory
                if os.path.exists(filename):
                    target_path = os.path.join(target_dir, filename)
                    if not os.path.exists(target_path):
                        shutil.move(filename, target_path)
                        moved_files.append(filename)
                        logger.info(f"📦 Moved {filename} to {target_path}")
                    else:
                        logger.info(f"📄 File already exists at target: {target_path}")
                else:
                    failed_files.append(filename)
                    logger.warning(f"⚠️ File not found for moving: {filename}")
            except Exception as e:
                failed_files.append(filename)
                logger.error(f"❌ Error moving {filename}: {e}")

        if moved_files:
            logger.info(f"✅ Successfully moved files: {moved_files}")
        if failed_files:
            logger.warning(f"⚠️ Failed to move files: {failed_files}")

    except Exception as e:
        logger.error(f"❌ Error in move_roshambo_outputs: {e}")

if __name__ == '__main__':
    # Create inpdata directory
    Path(ROSHAMBO_WORKING_DIR).mkdir(parents=True, exist_ok=True)

    print("🧬 Roshambo Flask API Starting...")
    print("=" * 50)
    print(f"📁 Working directory: {ROSHAMBO_WORKING_DIR}")

    # Check GPU availability on startup
    preferred_gpu, gpu_count = check_gpu_availability()
    if gpu_count > 0:
        print(f"🎯 Preferred GPU: {preferred_gpu}")

    print("🚀 Starting Flask server on 127.0.0.1:5000")
    print("📋 Available endpoints:")
    print("  GET  /health     - Health check with GPU status")
    print("  POST /similarity - Calculate molecular similarity")
    print("=" * 50)

    # Run the Flask app (localhost only for security)
    try:
        app.run(host='127.0.0.1', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Failed to start Flask server: {e}")
        logger.error(f"Failed to start Flask server: {e}")
