# Roshambo Flask API - Rebuilt and Optimized

This is a comprehensive Flask API wrapper for the Roshambo shape similarity engine, specifically designed for integration with Reinvent Scoring. The API has been rebuilt from scratch with enhanced error handling, comprehensive debugging, and optimized performance to prevent process kills and timeouts.

## Key Features

- **GPU Preference**: Automatically uses GPU 1 or 2 by default (not GPU 0) for better performance
- **Comprehensive Debugging**: Extensive logging and debug statements throughout the codebase
- **Error Handling**: Robust error handling to prevent process kills and timeouts
- **Batch Processing**: Intelligent batching to handle large datasets efficiently
- **Retry Logic**: Automatic retry mechanisms for failed API calls
- **Performance Monitoring**: Built-in performance tracking and timing
- **Security**: Localhost-only binding (127.0.0.1) for enhanced security

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   pip install git+https://github.com/rashatwi/roshambo.git
   ```

2. **Start the API (recommended):**
   ```bash
   python run_server.py
   ```

3. **Or use simple startup:**
   ```bash
   python start_api.py
   ```

4. **Test the API:**
   ```bash
   python test_api.py
   ```

## Files

- `app.py` - Main Flask application
- `start_api.py` - Startup script with environment checks
- `test_api.py` - Test suite for API validation
- `requirements.txt` - Python dependencies
- `configuration_examples.json` - Example configurations
- `ROSHAMBO_USAGE_GUIDE.md` - Comprehensive usage guide
- `inpdata/` - Working directory for Roshambo operations

## API Endpoints

### Health Check
```
GET /health
```

### Similarity Calculation
```
POST /similarity
Content-Type: application/json

{
  "reference_file": "/path/to/reference.sdf",
  "dataset_file": "/path/to/dataset.sdf",
  "ignore_hs": true,
  "n_confs": 0,
  "use_carbon_radii": true,
  "color": true,
  "sort_by": "ComboTanimoto",
  "write_to_file": true,
  "gpu_id": 0,
  "working_dir": "/path/to/working/dir"
}
```

## Integration with Reinvent

The simplified Roshambo scoring component (`reinvent_scoring/scoring/score_components/roshambo/roshambo_shape_similarity.py`) automatically:

1. Creates epoch-based folders for each reinforcement learning step
2. Generates conformers using RDKit (if configured)
3. Calls the Flask API for similarity calculations
4. Processes CSV output files to extract scores
5. Manages file cleanup and organization

## Configuration

See `configuration_examples.json` for various usage patterns:

- Basic shape similarity
- With RDKit conformer generation
- High-performance settings
- PROTAC design configurations
- Multi-objective scoring functions
- Debug configurations

## Troubleshooting

1. **API not starting:** Check if roshambo is installed and CUDA is available
2. **Import errors:** Ensure all dependencies are installed in the correct environment
3. **GPU issues:** Verify CUDA installation and GPU availability
4. **File not found:** Check file paths and permissions

## Architecture

```
Reinvent Scoring Component
         ↓ HTTP API calls
Roshambo Flask API
         ↓ Direct API calls  
Roshambo Engine
         ↓ GPU acceleration
CUDA/OpenCL Backend
```

This architecture provides:
- Better error handling and logging
- Easier deployment and scaling
- Separation of concerns
- Improved reliability
- Epoch-based file organization
