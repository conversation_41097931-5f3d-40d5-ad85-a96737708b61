# Roshambo Flask API

Clean and production-ready Flask API wrapper for the Roshambo shape similarity engine, designed for integration with Reinvent Scoring.

## Features

- **Simple and Clean**: Minimal code with essential functionality
- **Production Ready**: Robust error handling without over-engineering
- **GPU Support**: Configurable GPU selection (default GPU 0)
- **Debug Mode**: Optional debug logging via scoring component parameters

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   pip install git+https://github.com/rashatwi/roshambo.git
   ```

2. **Start the API:**
   ```bash
   python start_api.py
   ```

3. **Test the API:**
   ```bash
   python test_api.py
   ```

## API Endpoints

### Health Check
```
GET /health
```

### Similarity Calculation
```
POST /similarity
Content-Type: application/json

{
  "reference_file": "/path/to/reference.sdf",
  "dataset_file": "/path/to/dataset.smi",
  "ignore_hs": true,
  "n_confs": 0,
  "use_carbon_radii": true,
  "color": true,
  "sort_by": "ComboTanimoto",
  "write_to_file": true,
  "gpu_id": 0,
  "working_dir": "/path/to/working/dir"
}
```

## Integration with Reinvent Scoring

Use in your Reinvent configuration:

```json
{
  "component_type": "roshambo_shape_similarity",
  "name": "Shape Similarity",
  "weight": 1.0,
  "specific_parameters": {
    "reference_file": "data/target.sdf",
    "shape_weight": 0.5,
    "color_weight": 0.5,
    "roshambo_api_url": "http://127.0.0.1:5000",
    "gpu_id": 0,
    "debug": true,
    "save_overlays": true,
    "overlays_dir": "results/roshambo_overlays"
  }
}
```

## Files

- `app.py` - Main Flask application
- `start_api.py` - Startup script
- `test_api.py` - Test suite
- `requirements.txt` - Python dependencies
- `README.md` - This documentation
