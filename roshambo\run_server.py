#!/usr/bin/env python3
"""
Comprehensive server runner for Roshambo Flask API.
This script handles environment setup, dependency checking, and server startup.
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def check_environment():
    """Check if we're in the correct environment and directory."""
    print("🔍 Checking environment...")
    
    # Check if we're in the right directory
    current_dir = Path(__file__).parent
    required_files = ["app.py", "start_api.py", "requirements.txt"]
    
    missing_files = []
    for file in required_files:
        if not (current_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required files found")
    return True

def check_dependencies():
    """Check if all dependencies are installed."""
    print("📦 Checking dependencies...")
    
    required_packages = {
        "flask": "Flask",
        "requests": "requests", 
        "torch": "PyTorch",
        "pandas": "pandas",
        "numpy": "numpy"
    }
    
    missing_packages = []
    
    for package, display_name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {display_name} available")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {display_name} not available")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {missing_packages}")
        print("Install them with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_roshambo():
    """Check if Roshambo is available."""
    print("🧬 Checking Roshambo...")
    
    try:
        from roshambo.api import get_similarity_scores
        print("✅ Roshambo API available")
        return True
    except ImportError as e:
        print(f"❌ Roshambo not available: {e}")
        print("\n💡 To install Roshambo:")
        print("1. Activate your conda environment")
        print("2. pip install git+https://github.com/rashatwi/roshambo.git")
        return False

def check_gpu():
    """Check GPU availability."""
    print("🎯 Checking GPU...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✅ {gpu_count} GPU(s) available")
            
            # Show preferred GPU selection
            if gpu_count >= 3:
                preferred = 2
            elif gpu_count >= 2:
                preferred = 1
            else:
                preferred = 0
            
            print(f"🎯 Will use GPU {preferred} by default")
            
            for i in range(gpu_count):
                name = torch.cuda.get_device_name(i)
                memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  GPU {i}: {name} ({memory:.1f} GB)")
            
            return True
        else:
            print("⚠️ No GPU available, will use CPU")
            return False
    except ImportError:
        print("⚠️ PyTorch not available, cannot check GPU")
        return False

def start_server():
    """Start the Flask server."""
    print("\n🚀 Starting Roshambo Flask API...")
    print("=" * 50)
    
    try:
        # Import and run the app
        from start_api import main
        main()
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False
    
    return True

def run_tests():
    """Run basic API tests."""
    print("🧪 Running basic tests...")
    
    try:
        # Check if test file exists
        test_file = Path(__file__).parent / "test_api.py"
        if test_file.exists():
            print("📋 Running test suite...")
            result = subprocess.run([sys.executable, str(test_file)], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ Tests passed")
                return True
            else:
                print(f"❌ Tests failed: {result.stderr}")
                return False
        else:
            print("⚠️ Test file not found, skipping tests")
            return True
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def main():
    """Main function."""
    print("🧬 Roshambo Flask API Server Runner")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed")
        sys.exit(1)
    
    # Check Roshambo (optional)
    roshambo_available = check_roshambo()
    if not roshambo_available:
        print("\n⚠️ Roshambo not available, but continuing anyway")
        print("The API will start but similarity calculations will fail")
        
        response = input("\nContinue anyway? (y/N): ").strip().lower()
        if response != 'y':
            sys.exit(1)
    
    # Check GPU
    check_gpu()
    
    print("\n" + "=" * 50)
    print("🎉 All checks completed!")
    
    if roshambo_available:
        print("✅ Ready to start Roshambo Flask API")
    else:
        print("⚠️ Starting API without Roshambo (testing mode)")
    
    print("\n📋 Server will be available at: http://127.0.0.1:5000")
    print("📋 Endpoints:")
    print("  GET  /health     - Health check")
    print("  POST /similarity - Calculate similarity")
    
    # Ask user if they want to start
    response = input("\nStart server now? (Y/n): ").strip().lower()
    if response in ['', 'y', 'yes']:
        start_server()
    else:
        print("👋 Server startup cancelled")

if __name__ == "__main__":
    main()
