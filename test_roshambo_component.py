#!/usr/bin/env python3
"""
Test script for the clean Roshambo Shape Similarity Component.
This script tests the component with sample molecules.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the reinvent_scoring module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from reinvent_scoring.scoring.component_parameters import ComponentParameters
from reinvent_scoring.scoring.score_components.roshambo.roshambo_shape_similarity import RoshamboShapeSimilarity


def create_test_reference_sdf():
    """Create a simple test reference SDF file."""
    from rdkit import Chem
    from rdkit.Chem import AllChem
    
    # Create a simple molecule (benzene)
    mol = Chem.MolFromSmiles("c1ccccc1")
    mol = Chem.AddHs(mol)
    AllChem.EmbedMolecule(mol)
    mol.SetProp("_Name", "benzene_reference")
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(suffix=".sdf", delete=False)
    writer = Chem.SDWriter(temp_file.name)
    writer.write(mol)
    writer.close()
    
    return temp_file.name


def test_roshambo_component():
    """Test the Roshambo component with sample data."""
    print("🧪 Testing Roshambo Shape Similarity Component")
    
    # Create test reference file
    reference_file = create_test_reference_sdf()
    print(f"📄 Created test reference file: {reference_file}")
    
    try:
        # Test molecules (SMILES)
        test_molecules = [
            "c1ccccc1",  # benzene (should have high similarity)
            "CCCc1ccccc1",  # propylbenzene (moderate similarity)
            "CCCCCCCC",  # octane (low similarity)
            "CCO",  # ethanol (low similarity)
            "c1ccc2ccccc2c1"  # naphthalene (moderate similarity)
        ]
        
        # Configure component parameters
        parameters = ComponentParameters(
            component_type="roshambo_shape_similarity",
            name="test_roshambo",
            weight=1.0,
            specific_parameters={
                "reference_file": reference_file,
                "shape_weight": 0.5,
                "color_weight": 0.5,
                "n_confs": 50,  # Default as requested
                "ignore_hs": True,
                "use_carbon_radii": True,
                "gpu_id": 0,
                "roshambo_api_url": "http://127.0.0.1:5000",
                "save_overlays": True,
                "overlays_dir": "test_roshambo_overlays",
                "debug": True  # Enable debug mode for testing
            }
        )
        
        print("🔧 Initializing Roshambo component...")
        component = RoshamboShapeSimilarity(parameters)
        
        print(f"🧮 Testing with {len(test_molecules)} molecules...")
        
        # Calculate scores
        result = component.calculate_score(test_molecules, step=0)
        
        print("📊 Results:")
        print(f"   Total scores: {len(result.total_score)}")
        print(f"   Scores: {result.total_score}")
        print(f"   Mean score: {result.total_score.mean():.3f}")
        print(f"   Max score: {result.total_score.max():.3f}")
        print(f"   Min score: {result.total_score.min():.3f}")
        
        # Print individual results
        for i, (smiles, score) in enumerate(zip(test_molecules, result.total_score)):
            print(f"   {i}: {smiles:<20} -> {score:.3f}")
        
        print("✅ Test completed successfully!")
        
        # Check if overlay files were created
        overlays_dir = "test_roshambo_overlays/epoch_0"
        if os.path.exists(overlays_dir):
            files = os.listdir(overlays_dir)
            print(f"📁 Created files in {overlays_dir}: {files}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        if os.path.exists(reference_file):
            os.unlink(reference_file)
            print(f"🧹 Cleaned up reference file: {reference_file}")


if __name__ == "__main__":
    test_roshambo_component()
