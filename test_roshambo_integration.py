#!/usr/bin/env python3
"""
Comprehensive integration test for the rebuilt Roshambo scoring component and Flask API.
This script tests the complete pipeline from scoring component to Flask API.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the reinvent-scoring path to sys.path
sys.path.insert(0, os.path.abspath('.'))

def create_test_reference_file():
    """Create a simple test reference SDF file."""
    test_sdf_content = """
  Mrv2014 01010100002D          

  6  6  0  0  0  0            999 V2000
    1.2990    0.7500    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    2.5981    0.0000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    2.5981   -1.5000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    1.2990   -2.2500    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    0.0000   -1.5000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
    0.0000    0.0000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
  1  2  1  0  0  0  0
  2  3  1  0  0  0  0
  3  4  1  0  0  0  0
  4  5  1  0  0  0  0
  5  6  1  0  0  0  0
  6  1  1  0  0  0  0
M  END
$$$$
"""
    
    temp_dir = Path(tempfile.mkdtemp())
    ref_file = temp_dir / "reference.sdf"
    
    with open(ref_file, "w") as f:
        f.write(test_sdf_content.strip())
    
    return str(ref_file), str(temp_dir)

def test_scoring_component():
    """Test the Roshambo scoring component with debug enabled."""
    print("🧮 Testing Roshambo Scoring Component")
    print("=" * 60)
    
    try:
        from reinvent_scoring.scoring.component_parameters import ComponentParameters
        from reinvent_scoring.scoring.score_components.roshambo.roshambo_shape_similarity import RoshamboShapeSimilarity
        print("✅ Successfully imported Roshambo scoring component")
    except ImportError as e:
        print(f"❌ Failed to import Roshambo scoring component: {e}")
        return False
    
    # Create test reference file
    ref_file, temp_dir = create_test_reference_file()
    print(f"📄 Created test reference file: {ref_file}")
    
    try:
        # Create test configuration with debug enabled
        config = {
            "component_type": "roshambo_shape_similarity",
            "name": "Test Roshambo Shape Similarity",
            "weight": 1.0,
            "specific_parameters": {
                "reference_file": ref_file,
                "shape_weight": 0.6,
                "color_weight": 0.4,
                "n_confs": 0,
                "ignore_hs": True,
                "use_carbon_radii": True,
                "gpu_id": 1,  # Use GPU 1 by default
                "roshambo_api_url": "http://127.0.0.1:5000",
                "debug": True,  # Enable comprehensive debugging
                "max_retries": 2,
                "batch_size": 10,
                "save_overlays": True,
                "overlays_dir": os.path.join(temp_dir, "overlays")
            }
        }
        
        print("🔧 Configuration:")
        for key, value in config["specific_parameters"].items():
            print(f"  {key}: {value}")
        
        # Create component parameters
        params = ComponentParameters(
            component_type=config["component_type"],
            name=config["name"],
            weight=config["weight"],
            specific_parameters=config["specific_parameters"]
        )
        
        # Initialize the scoring component
        print("\n🚀 Initializing Roshambo scoring component...")
        component = RoshamboShapeSimilarity(params)
        print("✅ Component initialized successfully")
        
        # Test with simple molecules
        test_molecules = [
            "c1ccccc1",  # benzene
            "CCO",       # ethanol
            "CC(=O)O",   # acetic acid
            "c1ccc2ccccc2c1",  # naphthalene
            "INVALID_SMILES"   # invalid SMILES to test error handling
        ]
        
        print(f"\n🧪 Testing with {len(test_molecules)} molecules:")
        for i, mol in enumerate(test_molecules):
            print(f"  {i}: {mol}")
        
        # Calculate scores
        print("\n🔄 Calculating scores...")
        try:
            summary = component.calculate_score(test_molecules, step=0)
            scores = summary.total_score
            
            print(f"\n📊 Results:")
            print(f"  Scores shape: {scores.shape}")
            print(f"  Scores: {scores}")
            print(f"  Non-zero scores: {sum(1 for s in scores if s > 0)}/{len(scores)}")
            print(f"  Max score: {max(scores):.4f}")
            print(f"  Average score: {sum(scores)/len(scores):.4f}")
            
            # Check if overlays directory was created
            overlays_dir = config["specific_parameters"]["overlays_dir"]
            if os.path.exists(overlays_dir):
                files = os.listdir(overlays_dir)
                print(f"  Overlays directory created with {len(files)} items")
            
            print("✅ Scoring component test completed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error during score calculation: {e}")
            import traceback
            print(f"🔍 Traceback: {traceback.format_exc()}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up scoring component: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False
    
    finally:
        # Cleanup
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up temporary directory: {temp_dir}")
        except Exception as e:
            print(f"⚠️ Error cleaning up: {e}")

def test_flask_api_availability():
    """Test if the Flask API is available and responding."""
    print("\n🌐 Testing Flask API Availability")
    print("=" * 60)
    
    try:
        import requests
        
        api_url = "http://127.0.0.1:5000"
        
        # Test health endpoint
        print(f"🏥 Testing health endpoint at {api_url}/health...")
        try:
            response = requests.get(f"{api_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Health endpoint responding")
                print(f"  Status: {data.get('status')}")
                print(f"  GPU count: {data.get('gpu', {}).get('count', 0)}")
                print(f"  Preferred GPU: {data.get('gpu', {}).get('preferred_gpu', 'N/A')}")
                print(f"  Roshambo available: {data.get('roshambo', {}).get('available', False)}")
                return True
            else:
                print(f"❌ Health endpoint returned status {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to Flask API")
            print("💡 Make sure to start the Flask API first:")
            print("   cd roshambo && python run_server.py")
            return False
        except Exception as e:
            print(f"❌ Error testing Flask API: {e}")
            return False
            
    except ImportError:
        print("❌ requests library not available")
        return False

def main():
    """Main test function."""
    print("🧬 Roshambo Integration Test Suite")
    print("=" * 80)
    print("This test validates the complete integration between the")
    print("Roshambo scoring component and Flask API.")
    print("=" * 80)
    
    # Test Flask API availability first
    api_available = test_flask_api_availability()
    
    # Test scoring component
    component_test = test_scoring_component()
    
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY")
    print("=" * 80)
    
    if api_available:
        print("✅ Flask API: Available and responding")
    else:
        print("❌ Flask API: Not available or not responding")
        print("   Start the API with: cd roshambo && python run_server.py")
    
    if component_test:
        print("✅ Scoring Component: Working correctly")
    else:
        print("❌ Scoring Component: Failed")
    
    if api_available and component_test:
        print("\n🎉 INTEGRATION TEST PASSED")
        print("The Roshambo scoring component and Flask API are working correctly!")
    else:
        print("\n⚠️ INTEGRATION TEST INCOMPLETE")
        if not api_available:
            print("Please start the Flask API and run the test again.")
        if not component_test:
            print("Please check the scoring component configuration.")
    
    print("\n💡 For detailed debugging, check the debug output above.")
    print("💡 The scoring component has comprehensive logging enabled.")

if __name__ == "__main__":
    main()
