"""
Clean Roshambo Shape Similarity Component for Reinvent Scoring.
This component uses the Roshambo Flask API for GPU-accelerated molecular shape comparison.
"""

import os
import shutil
import pandas as pd
import numpy as np
import requests
from pathlib import Path
from typing import List

from reinvent_scoring.scoring.component_parameters import ComponentParameters
from reinvent_scoring.scoring.score_components.base_score_component import BaseScoreComponent
from reinvent_scoring.scoring.score_summary import ComponentSummary
from reinvent_scoring.scoring.enums.roshambo_specific_parameters_enum import RoshamboSpecificParametersEnum


class RoshamboShapeSimilarity(BaseScoreComponent):
    """
    Clean Roshambo Shape Similarity Component.
    Uses Flask API for GPU-accelerated molecular shape comparison with epoch-based organization.
    """

    def __init__(self, parameters: ComponentParameters):
        super().__init__(parameters)

        # Initialize parameter enum
        self.param_enum = RoshamboSpecificParametersEnum()

        # Basic parameters
        self.reference_file = self.parameters.specific_parameters.get(self.param_enum.REFERENCE_FILE, "")
        self.shape_weight = self.parameters.specific_parameters.get(self.param_enum.SHAPE_WEIGHT, 0.5)
        self.color_weight = self.parameters.specific_parameters.get(self.param_enum.COLOR_WEIGHT, 0.5)
        self.n_confs = self.parameters.specific_parameters.get(self.param_enum.N_CONFS, 50)  # Default 50 as requested
        self.ignore_hs = self.parameters.specific_parameters.get(self.param_enum.IGNORE_HS, True)
        self.use_carbon_radii = self.parameters.specific_parameters.get(self.param_enum.USE_CARBON_RADII, True)
        self.gpu_id = self.parameters.specific_parameters.get(self.param_enum.GPU_ID, 0)

        # Flask API configuration
        self.roshambo_api_url = self.parameters.specific_parameters.get("roshambo_api_url", "http://127.0.0.1:5000")

        # Overlay saving parameters
        self.save_overlays = self.parameters.specific_parameters.get(self.param_enum.SAVE_OVERLAYS, True)
        self.overlays_dir = self.parameters.specific_parameters.get(self.param_enum.OVERLAYS_DIR, "roshambo_overlays")

        # Debug mode
        self.debug = self.parameters.specific_parameters.get("debug", False)

        # Create overlays directory
        if self.save_overlays:
            Path(self.overlays_dir).mkdir(parents=True, exist_ok=True)

        # Validate reference file
        if not self.reference_file:
            raise ValueError("Reference file must be provided")
        if not os.path.exists(self.reference_file):
            raise FileNotFoundError(f"Reference file not found: {self.reference_file}")

        if self.debug:
            print(f"🔧 Roshambo initialized: reference={self.reference_file}, n_confs={self.n_confs}, gpu_id={self.gpu_id}")
            print(f"🔧 API URL: {self.roshambo_api_url}, overlays_dir: {self.overlays_dir}")

        # Initialize epoch counter
        self.current_epoch = 0

    def calculate_score(self, molecules: List, step=-1) -> ComponentSummary:
        """Calculate shape similarity scores for a list of molecules."""
        if self.debug:
            print(f"🧮 Roshambo calculate_score called with {len(molecules)} molecules, step={step}")

        # Convert input to SMILES strings
        smiles_list = []
        for mol in molecules:
            if isinstance(mol, str):
                smiles_list.append(mol)
            else:
                try:
                    from rdkit import Chem
                    smiles = Chem.MolToSmiles(mol)
                    smiles_list.append(smiles)
                except:
                    smiles_list.append("")  # Empty string will result in zero score

        if self.debug:
            print(f"📝 Converted {len(smiles_list)} molecules to SMILES")

        # Calculate scores
        scores = self._calculate_shape_scores(smiles_list, step)

        if self.debug:
            print(f"📊 Calculated {len(scores)} scores, mean: {np.mean(scores):.3f}")

        # Create and return component summary
        score_summary = ComponentSummary(total_score=scores, parameters=self.parameters)
        return score_summary

    def _calculate_shape_scores(self, smiles_list: List[str], step: int) -> np.array:
        """Calculate shape similarity scores using Roshambo Flask API."""
        if self.debug:
            print(f"⚙️ Processing {len(smiles_list)} SMILES for step {step}")

        if not smiles_list:
            return np.array([], dtype=np.float32)

        # Create epoch folder
        epoch_folder = os.path.join(self.overlays_dir, f"epoch_{step}")
        Path(epoch_folder).mkdir(parents=True, exist_ok=True)

        if self.debug:
            print(f"📁 Created epoch folder: {epoch_folder}")

        # Create dataset SDF file with RDKit molecules
        dataset_filename = self._create_dataset_sdf(smiles_list, epoch_folder, step)

        # Copy reference file to epoch folder
        reference_filename = os.path.basename(self.reference_file)
        epoch_reference_file = os.path.join(epoch_folder, reference_filename)
        shutil.copy2(self.reference_file, epoch_reference_file)

        if self.debug:
            print(f"📄 Created dataset file: {dataset_filename}")
            print(f"📄 Copied reference file: {epoch_reference_file}")

        # Call Roshambo Flask API with just filenames (not full paths)
        scores = self._call_roshambo_api(reference_filename, os.path.basename(dataset_filename), epoch_folder, len(smiles_list))
        return np.array(scores, dtype=np.float32)

    def _create_dataset_sdf(self, smiles_list: List[str], epoch_folder: str, step: int) -> str:
        """Create SDF file with RDKit molecules from SMILES."""
        dataset_file = os.path.join(epoch_folder, f"dataset_{step}.sdf")

        if self.debug:
            print(f"🧪 Creating SDF file with {len(smiles_list)} molecules")

        from rdkit import Chem
        from rdkit.Chem import AllChem
        writer = Chem.SDWriter(dataset_file)

        successful_molecules = 0

        for i, smiles in enumerate(smiles_list):
            if not smiles:
                continue

            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                # Add hydrogens and generate 3D coordinates
                mol = Chem.AddHs(mol)
                if AllChem.EmbedMolecule(mol) == 0:  # Success
                    mol.SetProp("_Name", f"mol_{i}")
                    writer.write(mol)
                    successful_molecules += 1
                elif self.debug:
                    print(f"⚠️ Failed to embed molecule {i}: {smiles}")
            elif self.debug:
                print(f"⚠️ Invalid SMILES {i}: {smiles}")

        writer.close()

        if self.debug:
            print(f"✅ Created SDF file: {dataset_file} with {successful_molecules}/{len(smiles_list)} successful molecules")

        if successful_molecules == 0:
            if self.debug:
                print(f"❌ No molecules were successfully embedded! This will cause Roshambo to fail.")

        return dataset_file

    def _call_roshambo_api(self, reference_file: str, dataset_file: str, epoch_folder: str, expected_count: int) -> List[float]:
        """Call Roshambo Flask API and return scores."""
        # Prepare API request data as specified
        api_data = {
            "reference_file": reference_file,
            "dataset_file": dataset_file,
            "ignore_hs": self.ignore_hs,
            "n_confs": self.n_confs,  # Use configured n_confs (default 50)
            "use_carbon_radii": self.use_carbon_radii,
            "color": self.color_weight > 0,
            "sort_by": "ComboTanimoto",
            "write_to_file": True,
            "gpu_id": self.gpu_id,
            "working_dir": epoch_folder
        }

        if self.debug:
            print(f"🌐 Calling Roshambo API at {self.roshambo_api_url}/similarity")
            print(f"📋 API data: {api_data}")

        try:
            # Call Flask API
            response = requests.post(
                f"{self.roshambo_api_url}/similarity",
                json=api_data,
                timeout=300  # 5 minute timeout
            )

            if self.debug:
                print(f"📡 API response status: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    if self.debug:
                        print(f"✅ API call successful, execution time: {result.get('execution_time', 'N/A')}s")

                    # Read CSV file and extract scores
                    csv_file = os.path.join(epoch_folder, "roshambo.csv")
                    return self._extract_scores_from_csv(csv_file, epoch_folder, expected_count)
                else:
                    if self.debug:
                        print(f"❌ API returned error: {result.get('error', 'Unknown error')}")
                    return [0.0] * expected_count  # Return zeros for expected count
            else:
                if self.debug:
                    print(f"❌ API request failed with status {response.status_code}: {response.text}")
                return [0.0] * expected_count  # Return zeros for expected count

        except Exception as e:
            if self.debug:
                print(f"❌ Error calling Roshambo API: {e}")
            return [0.0] * expected_count  # Return zeros for expected count

    def _extract_scores_from_csv(self, csv_file: str, epoch_folder: str, expected_count: int) -> List[float]:
        """Extract scores from Roshambo CSV output."""
        if self.debug:
            print(f"📊 Extracting scores from CSV: {csv_file}")

        if not os.path.exists(csv_file):
            if self.debug:
                print(f"❌ CSV file not found: {csv_file}")
            return [0.0] * expected_count

        try:
            # Read CSV with tab delimiter as specified
            df = pd.read_csv(csv_file, sep='\t')

            if self.debug:
                print(f"📈 CSV loaded: {df.shape[0]} rows, {df.shape[1]} columns")
                print(f"📋 Columns: {df.columns.tolist()}")
                if df.shape[0] > 0:
                    print(f"📝 Sample data:\n{df.head(3)}")

            # Create a mapping from molecule names to scores
            mol_scores = {}
            for _, row in df.iterrows():
                name = row.get("Molecule", "")
                if name and name.startswith("mol_"):
                    try:
                        # Extract molecule index from name like "mol_58_0" -> 58
                        parts = name.split("_")
                        if len(parts) >= 2:
                            idx = int(parts[1])

                            # Get individual scores
                            shape_score = float(row.get("ShapeTanimoto", 0.0))
                            color_score = float(row.get("ColorTanimoto", 0.0))
                            combo_score = float(row.get("ComboTanimoto", 0.0))

                            # Use ComboTanimoto directly or calculate weighted combination
                            if combo_score > 0:
                                final_score = combo_score
                            else:
                                # Calculate weighted combination as fallback
                                total_weight = self.shape_weight + self.color_weight
                                if total_weight > 0:
                                    final_score = (self.shape_weight * shape_score +
                                                 self.color_weight * color_score) / total_weight
                                else:
                                    final_score = shape_score  # Default to shape score

                            # Keep the best score for each molecule (in case of multiple conformers)
                            mol_scores[idx] = max(mol_scores.get(idx, 0.0), final_score)

                    except Exception as e:
                        if self.debug:
                            print(f"⚠️ Error processing row for {name}: {e}")
                        continue

            # Convert to ordered list
            if mol_scores:
                max_idx = max(mol_scores.keys())
                scores = [mol_scores.get(i, 0.0) for i in range(max_idx + 1)]
            else:
                scores = []

            if self.debug:
                print(f"✅ Extracted {len(scores)} scores, mean: {np.mean(scores):.3f}")

            # Move roshambo output files to overlays directory for inspection
            self._move_roshambo_outputs(epoch_folder)

            return scores

        except Exception as e:
            if self.debug:
                print(f"❌ Error reading CSV file {csv_file}: {e}")
            return []

    def _move_roshambo_outputs(self, epoch_folder: str):
        """Move roshambo output files (mols.sdf, hits.sdf) to epoch folder if they exist."""
        output_files = ["mols.sdf", "hits.sdf"]

        for filename in output_files:
            src_file = os.path.join(epoch_folder, filename)
            if os.path.exists(src_file) and self.debug:
                print(f"📁 Found roshambo output: {src_file}")
                # Files are already in the epoch folder, just log their presence