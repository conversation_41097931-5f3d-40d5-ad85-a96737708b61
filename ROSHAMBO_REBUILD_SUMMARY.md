# Roshambo Flask Wrapper and Scoring Component - Rebuild Summary

## Overview

The Roshambo Flask wrapper and scoring component have been completely rebuilt from scratch to address process kill issues, improve performance, and provide comprehensive debugging capabilities. This rebuild focuses on reliability, error handling, and preventing timeouts that were causing process termination.

## Key Improvements

### 1. Flask API Wrapper (`roshambo/app.py`)

**Enhanced Features:**
- **GPU Preference**: Automatically uses GPU 1 or 2 by default (not GPU 0)
- **Comprehensive Logging**: Detailed logging with emojis for easy identification
- **Error Handling**: Robust error handling at every level
- **Adaptive Timeouts**: Timeout scales with dataset size
- **File Validation**: Extensive file existence and size checks
- **Response Processing**: Detailed API response validation
- **Security**: Localhost-only binding (127.0.0.1) for security

**Debug Features:**
- GPU availability detection with memory information
- System resource monitoring (memory, disk usage)
- Detailed request/response logging
- File operation tracking
- Execution time monitoring

### 2. Enhanced Scoring Component (`reinvent_scoring/scoring/score_components/roshambo/roshambo_shape_similarity.py`)

**Performance Optimizations:**
- **Batch Processing**: Large datasets processed in configurable batches
- **Retry Logic**: Automatic retry with exponential backoff
- **Adaptive Timeouts**: Timeout per molecule configuration
- **Memory Management**: Efficient memory usage and cleanup
- **Process Monitoring**: Performance tracking and statistics

**Debug Enhancements:**
- **Comprehensive Logging**: Debug statements throughout the entire pipeline
- **Molecule Tracking**: Individual molecule processing status
- **API Call Monitoring**: Detailed API request/response logging
- **File Operation Logging**: Complete file creation and validation tracking
- **Error Tracing**: Full stack traces for debugging
- **Performance Metrics**: Execution time and throughput monitoring

**Error Prevention:**
- **Input Validation**: Extensive input validation and sanitization
- **Graceful Degradation**: Returns zeros instead of crashing
- **Resource Management**: Proper cleanup and resource management
- **Timeout Handling**: Prevents hanging processes

### 3. Startup and Management Scripts

**`roshambo/start_api.py`:**
- Environment validation
- Dependency checking
- GPU detection and preference selection
- Roshambo availability verification
- Enhanced error reporting

**`roshambo/run_server.py`:**
- Comprehensive environment checking
- Dependency validation
- Interactive startup process
- Test execution capability
- User-friendly error messages

**`roshambo/test_api.py`:**
- Health endpoint testing
- Similarity calculation validation
- Error handling verification
- Performance testing

### 4. Integration Testing

**`test_roshambo_integration.py`:**
- Complete pipeline testing
- Scoring component validation
- Flask API connectivity testing
- Debug output verification
- Performance monitoring

## Configuration Improvements

### New Parameters

```json
{
  "debug": true,              // Enable comprehensive debugging
  "max_retries": 3,           // Number of retry attempts
  "retry_delay": 5,           // Delay between retries (seconds)
  "batch_size": 100,          // Molecules per batch
  "timeout_per_molecule": 10, // Timeout per molecule (seconds)
  "gpu_id": 1                 // Preferred GPU (1 or 2, not 0)
}
```

### Enhanced Error Handling

- **Connection Errors**: Detailed connection error messages with troubleshooting tips
- **Timeout Errors**: Adaptive timeout handling with retry logic
- **File Errors**: Comprehensive file validation and error reporting
- **API Errors**: Detailed API response processing and error handling
- **Memory Errors**: Memory usage monitoring and batch processing

## Debugging Features

### Comprehensive Logging

The rebuilt system provides extensive logging at every level:

1. **Initialization**: Component setup and configuration validation
2. **Input Processing**: Molecule conversion and validation
3. **File Operations**: File creation, validation, and cleanup
4. **API Communication**: Request/response details and timing
5. **Score Processing**: CSV parsing and score extraction
6. **Performance Monitoring**: Execution times and throughput

### Debug Output Examples

```
🧮 ===== ROSHAMBO CALCULATE_SCORE START =====
📊 Input: 50 molecules, step=0
🔢 Total molecules processed so far: 0
📞 Total API calls made so far: 0
🔄 Converting 50 molecules to SMILES...
✅ Conversion complete: 50 SMILES, 0 errors
🚀 Starting shape similarity calculation...
📁 Created epoch folder: roshambo_overlays/epoch_0
📝 Creating SMILES file for Roshambo conformer generation
📝 Written 50/50 valid SMILES to dataset_0.smi
✅ Dataset file created: dataset_0.smi (1234 bytes)
🌐 Calling Roshambo API...
🌐 _call_roshambo_api: Starting API call for 50 molecules
📊 File sizes - Reference: 2048 bytes, Dataset: 1234 bytes
🔧 API request data prepared
🌐 Calling Roshambo API #1 at http://127.0.0.1:5000/similarity
⏱️ Using adaptive timeout: 800 seconds for 50 molecules
🚀 Sending POST request...
📡 API call completed in 45.23 seconds
📊 Response status: 200
✅ API request successful (200 OK)
✅ API reported success
⏱️ API execution time: 45.12s on GPU 1
📄 Found CSV file: roshambo.csv (5678 bytes)
📊 _extract_scores_from_csv: Processing roshambo.csv for 50 molecules
📄 CSV file size: 5678 bytes
✅ Successfully read CSV with tab separator
📊 CSV loaded: 50 rows, 11 columns
✅ All required columns present: ['Molecule', 'ShapeTanimoto', 'ColorTanimoto']
🔄 Processing 50 rows to extract scores...
📊 Processed 50 rows, 0 parsing errors
🎯 Found scores for 50 unique molecules
📈 Score range: idx 0-49, avg score: 0.6234
✅ Extracted 50/50 non-zero scores
📊 Score stats: max=0.8765, avg_non_zero=0.6234
✅ Received 50 scores from API
📈 Score statistics: 50/50 non-zero, avg=0.6234, max=0.8765
⏱️ Execution time: 47.56s
📊 Total execution time: 47.56s
🧮 ===== ROSHAMBO CALCULATE_SCORE END =====
```

## Performance Improvements

### Bottleneck Resolution

1. **Process Kills**: Prevented through proper timeout handling and resource management
2. **Memory Issues**: Resolved with batch processing and memory cleanup
3. **API Timeouts**: Fixed with adaptive timeouts and retry logic
4. **File Handling**: Improved with comprehensive validation and error handling

### Optimization Features

- **Batch Processing**: Configurable batch sizes for large datasets
- **Adaptive Timeouts**: Scale with dataset size and complexity
- **GPU Selection**: Prefer GPU 1/2 for better performance
- **Memory Management**: Efficient memory usage and cleanup
- **Retry Logic**: Automatic retry with exponential backoff

## Usage Instructions

### Starting the Flask API

```bash
# Recommended: Use the comprehensive server runner
cd roshambo
python run_server.py

# Alternative: Use the simple startup script
python start_api.py

# Direct: Run the Flask app
python app.py
```

### Testing the Integration

```bash
# Test the complete integration
python test_roshambo_integration.py

# Test just the Flask API
cd roshambo
python test_api.py
```

### Configuration for Reinvent

```json
{
  "component_type": "roshambo_shape_similarity",
  "name": "Shape Similarity",
  "weight": 1.0,
  "specific_parameters": {
    "reference_file": "data/target.sdf",
    "shape_weight": 0.5,
    "color_weight": 0.5,
    "roshambo_api_url": "http://127.0.0.1:5000",
    "gpu_id": 1,
    "debug": true,
    "max_retries": 3,
    "batch_size": 100,
    "timeout_per_molecule": 10,
    "save_overlays": true,
    "overlays_dir": "results/roshambo_overlays"
  }
}
```

## Troubleshooting

### Common Issues and Solutions

1. **Process Kills**: 
   - Enable debug mode to see detailed logs
   - Reduce batch_size if memory issues
   - Increase timeout_per_molecule for complex molecules

2. **API Connection Errors**:
   - Ensure Flask server is running on 127.0.0.1:5000
   - Check firewall settings
   - Verify API health endpoint

3. **GPU Issues**:
   - Check GPU availability with health endpoint
   - Verify CUDA installation
   - Use GPU 1 or 2 instead of GPU 0

4. **Performance Issues**:
   - Monitor debug output for bottlenecks
   - Adjust batch_size and timeout settings
   - Check system resources

## Summary

The rebuilt Roshambo Flask wrapper and scoring component provide:

- **Reliability**: Robust error handling prevents process kills
- **Performance**: Optimized for large datasets with batch processing
- **Debugging**: Comprehensive logging for troubleshooting
- **Security**: Localhost-only binding for enhanced security
- **Flexibility**: Configurable parameters for different use cases
- **Monitoring**: Built-in performance tracking and statistics

The system is now production-ready with comprehensive error handling, debugging capabilities, and performance optimizations to prevent the process kill issues you were experiencing.
