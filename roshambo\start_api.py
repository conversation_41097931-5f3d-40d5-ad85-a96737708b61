#!/usr/bin/env python3
"""
Startup script for Roshambo Flask API.
Clean and simple implementation.
"""

import os
import sys
from pathlib import Path

def setup_environment():
    """Set up the environment for Roshambo."""
    print("🔧 Setting up Roshambo environment...")
    
    # Check if we're in the right directory
    current_dir = Path(__file__).parent
    app_file = current_dir / "app.py"
    
    if not app_file.exists():
        print(f"❌ app.py not found in {current_dir}")
        return False
    
    # Create necessary directories
    inpdata_dir = Path("inpdata")
    inpdata_dir.mkdir(exist_ok=True)
    print(f"✅ Created directory: {inpdata_dir}")

    # Check if roshambo is available
    try:
        from roshambo.api import get_similarity_scores
        print("✅ Roshambo API is available")
        return True
    except ImportError as e:
        print(f"❌ Roshambo API not found: {e}")
        print("Please install roshambo in your conda environment")
        return False

def check_gpu():
    """Check GPU availability."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✅ GPU available: {gpu_count} device(s)")
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"  GPU {i}: {gpu_name}")
            return True
        else:
            print("⚠️ No GPU available, will use CPU")
            return False
    except ImportError:
        print("⚠️ PyTorch not available, cannot check GPU")
        return False

def start_api(host='127.0.0.1', port=5000, debug=True):
    """Start the Flask API server."""
    print(f"🚀 Starting Roshambo Flask API on {host}:{port}")

    try:
        from app import app
        app.run(host=host, port=port, debug=debug)
    except ImportError as e:
        print(f"❌ Failed to import Flask app: {e}")
        raise
    except Exception as e:
        print(f"❌ Failed to start Flask server: {e}")
        raise

def main():
    """Main function."""
    print("🧬 Roshambo Flask API Startup")
    print("=" * 40)

    # Setup environment
    if not setup_environment():
        print("❌ Environment setup failed")
        sys.exit(1)

    # Check GPU
    check_gpu()

    # Start API
    try:
        start_api()
    except KeyboardInterrupt:
        print("\n👋 Shutting down Roshambo API")
    except Exception as e:
        print(f"❌ Error starting API: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
