#!/usr/bin/env python3
"""
Startup script for Roshambo Flask API.
This script sets up the environment and starts the Flask API server.
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_environment():
    """Set up the environment for Roshambo."""
    print("🔧 Setting up Roshambo environment...")

    # Check if we're in the right directory
    current_dir = Path(__file__).parent
    app_file = current_dir / "app.py"

    if not app_file.exists():
        print(f"❌ app.py not found in {current_dir}")
        return False

    # Create necessary directories
    inpdata_dir = Path("inpdata")
    inpdata_dir.mkdir(exist_ok=True)
    print(f"✅ Created directory: {inpdata_dir}")

    # Check if roshambo is available
    try:
        from roshambo.api import get_similarity_scores
        print("✅ Roshambo API is available")
        return True
    except ImportError as e:
        print(f"❌ Roshambo API not found: {e}")
        print("Please install roshambo in your conda environment:")
        print("  conda activate your_roshambo_env")
        print("  pip install git+https://github.com/rashatwi/roshambo.git")
        return False

def check_dependencies():
    """Check if required dependencies are available."""
    print("📦 Checking dependencies...")

    required_packages = ["flask", "torch"]
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} available")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} not available")

    if missing_packages:
        print(f"⚠️ Missing packages: {missing_packages}")
        print("Please install them with: pip install " + " ".join(missing_packages))
        return False

    return True

def check_gpu():
    """Check GPU availability and prefer GPU 1 or 2."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✅ GPU available: {gpu_count} device(s)")

            # Prefer GPU 1 or 2, avoid GPU 0
            if gpu_count >= 3:
                preferred_gpu = 2
                print("🎯 Will use GPU 2 (preferred for multi-GPU systems)")
            elif gpu_count >= 2:
                preferred_gpu = 1
                print("🎯 Will use GPU 1 (preferred over GPU 0)")
            else:
                preferred_gpu = 0
                print("⚠️ Only GPU 0 available, will use it as fallback")

            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  GPU {i}: {gpu_name} ({memory_total:.1f} GB)")
            return True
        else:
            print("⚠️ No GPU available, will use CPU")
            return False
    except ImportError:
        print("⚠️ PyTorch not available, cannot check GPU")
        return False

def start_api(host='127.0.0.1', port=5000, debug=True):
    """Start the Flask API server with try-catch."""
    print(f"🚀 Starting Roshambo Flask API on {host}:{port}")
    print("📋 Available endpoints:")
    print("  GET  /health     - Health check with GPU status")
    print("  POST /similarity - Calculate molecular similarity")
    print("=" * 50)

    try:
        # Import and run the Flask app
        from app import app
        app.run(host=host, port=port, debug=debug)
    except ImportError as e:
        print(f"❌ Failed to import Flask app: {e}")
        raise
    except Exception as e:
        print(f"❌ Failed to start Flask server: {e}")
        raise

def main():
    """Main function."""
    print("🧬 Roshambo Flask API Startup")
    print("=" * 40)

    # Setup environment
    if not setup_environment():
        print("❌ Environment setup failed")
        sys.exit(1)

    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed")
        sys.exit(1)

    # Check GPU
    check_gpu()

    # Start API
    try:
        start_api()
    except KeyboardInterrupt:
        print("\n👋 Shutting down Roshambo API")
    except Exception as e:
        print(f"❌ Error starting API: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
