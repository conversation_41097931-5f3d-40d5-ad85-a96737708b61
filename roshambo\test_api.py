#!/usr/bin/env python3
"""
Simple test script for Roshambo Flask API.
"""

import os
import requests
import tempfile
from pathlib import Path

def create_test_files():
    """Create simple test files."""
    test_dir = Path(tempfile.mkdtemp())
    
    # Create reference SDF
    ref_sdf = test_dir / "reference.sdf"
    with open(ref_sdf, "w") as f:
        f.write("""
  Mrv2014 01010100002D          

  1  0  0  0  0  0            999 V2000
    0.0000    0.0000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
M  END
$$$$
""")
    
    # Create dataset SMILES
    dataset_smi = test_dir / "dataset.smi"
    with open(dataset_smi, "w") as f:
        f.write("C mol_0\n")
        f.write("CC mol_1\n")
        f.write("CCC mol_2\n")
    
    return str(ref_sdf), str(dataset_smi)

def test_health_endpoint(api_url="http://127.0.0.1:5000"):
    """Test the health endpoint."""
    print("Testing health endpoint...")
    
    try:
        response = requests.get(f"{api_url}/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint working")
            print(f"Status: {data.get('status')}")
            print(f"GPU count: {data.get('gpu_count', 0)}")
            print(f"Roshambo available: {data.get('roshambo_available', False)}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the Flask server is running.")
        return False
    except Exception as e:
        print(f"❌ Health test failed: {e}")
        return False

def test_similarity_endpoint(api_url="http://127.0.0.1:5000"):
    """Test the similarity calculation endpoint."""
    print("Testing similarity endpoint...")
    
    # Create test files
    ref_file, dataset_file = create_test_files()
    
    # Prepare test data
    test_data = {
        "reference_file": ref_file,
        "dataset_file": dataset_file,
        "ignore_hs": True,
        "n_confs": 0,
        "use_carbon_radii": True,
        "color": True,
        "sort_by": "ComboTanimoto",
        "write_to_file": True,
        "gpu_id": 0
    }
    
    try:
        response = requests.post(
            f"{api_url}/similarity",
            json=test_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Similarity calculation successful")
                print(f"GPU used: {result.get('gpu_used')}")
                
                # Check output files
                output_files = result.get("output_files", {})
                for file_type, file_path in output_files.items():
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"{file_type}: {file_path} ({file_size} bytes)")
                    else:
                        print(f"{file_type}: {file_path} (not found)")
                
                return True
            else:
                print(f"❌ API returned error: {result.get('error')}")
                return False
        else:
            print(f"❌ Similarity request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Similarity test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧬 Roshambo Flask API Test Suite")
    print("=" * 40)
    
    api_url = "http://127.0.0.1:5000"
    
    # Test health endpoint
    if not test_health_endpoint(api_url):
        print("❌ Health test failed. Make sure the API is running.")
        return
    
    print()
    
    # Test similarity endpoint
    if test_similarity_endpoint(api_url):
        print("✅ All tests passed")
    else:
        print("❌ Similarity test failed")

if __name__ == "__main__":
    main()
